import React, { useState, useEffect, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { Checkbox } from 'primereact/checkbox';
import { Card } from 'primereact/card';
import { fetchBitcoinData } from '../api/csvDataService';
import { OHLCData, ChartConfig } from '../types';
import {
  convertToOHLC,
  calculateSMA,
  calculateEMA,
  calculateRSI,
  formatVolume
} from '../utils/technicalIndicators';

interface AdvancedBitcoinChartProps {
  height?: number;
}

const AdvancedBitcoinChart: React.FC<AdvancedBitcoinChartProps> = ({ height = 600 }) => {
  const [ohlcData, setOhlcData] = useState<OHLCData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [chartConfig, setChartConfig] = useState<ChartConfig>({
    type: 'candlestick',
    showVolume: true,
    indicators: {
      rsi: false,
      sma: false,
      ema: false,
      smaPeriod: 20,
      emaPeriod: 12
    }
  });

  const chartTypeOptions = [
    { label: 'Candlestick', value: 'candlestick' },
    { label: 'Line', value: 'line' },
    { label: 'Area', value: 'area' }
  ];

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const csvData = await fetchBitcoinData();
        const ohlc = convertToOHLC(csvData);
        // Reverse to get chronological order (oldest first)
        setOhlcData(ohlc.reverse());
      } catch (err: any) {
        console.error('Error loading Bitcoin data:', err);
        setError(err.message || 'Failed to load Bitcoin data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const chartData = useMemo(() => {
    if (ohlcData.length === 0) return null;

    const dates = ohlcData.map(d => d.date);
    const prices = ohlcData.map(d => d.close);
    const volumes = ohlcData.map(d => d.volume);
    
    // Calculate technical indicators
    const smaData = chartConfig.indicators.sma ? calculateSMA(prices, chartConfig.indicators.smaPeriod) : [];
    const emaData = chartConfig.indicators.ema ? calculateEMA(prices, chartConfig.indicators.emaPeriod) : [];
    const rsiData = chartConfig.indicators.rsi ? calculateRSI(prices) : [];

    const series: any[] = [];

    // Main price series
    if (chartConfig.type === 'candlestick') {
      series.push({
        name: 'Bitcoin Price',
        type: 'candlestick',
        data: ohlcData.map(d => [d.open, d.close, d.low, d.high]),
        itemStyle: {
          color: '#00da3c',
          color0: '#ec0000',
          borderColor: '#00da3c',
          borderColor0: '#ec0000'
        },
        xAxisIndex: 0,
        yAxisIndex: 0
      });
    } else if (chartConfig.type === 'line') {
      series.push({
        name: 'Bitcoin Price',
        type: 'line',
        data: prices,
        smooth: true,
        lineStyle: { color: '#f7931a', width: 2 },
        xAxisIndex: 0,
        yAxisIndex: 0
      });
    } else if (chartConfig.type === 'area') {
      series.push({
        name: 'Bitcoin Price',
        type: 'line',
        data: prices,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(247, 147, 26, 0.8)' },
              { offset: 1, color: 'rgba(247, 147, 26, 0.1)' }
            ]
          }
        },
        lineStyle: { color: '#f7931a', width: 2 },
        xAxisIndex: 0,
        yAxisIndex: 0
      });
    }

    // Moving averages
    if (chartConfig.indicators.sma && smaData.length > 0) {
      series.push({
        name: `SMA(${chartConfig.indicators.smaPeriod})`,
        type: 'line',
        data: smaData,
        smooth: true,
        lineStyle: { color: '#ff6b6b', width: 1.5 },
        xAxisIndex: 0,
        yAxisIndex: 0
      });
    }

    if (chartConfig.indicators.ema && emaData.length > 0) {
      series.push({
        name: `EMA(${chartConfig.indicators.emaPeriod})`,
        type: 'line',
        data: emaData,
        smooth: true,
        lineStyle: { color: '#4ecdc4', width: 1.5 },
        xAxisIndex: 0,
        yAxisIndex: 0
      });
    }

    // Volume series
    if (chartConfig.showVolume) {
      series.push({
        name: 'Volume',
        type: 'bar',
        data: volumes,
        itemStyle: {
          color: function(params: any) {
            const index = params.dataIndex;
            if (index === 0) return '#666';
            return ohlcData[index].close >= ohlcData[index].open ? '#00da3c' : '#ec0000';
          }
        },
        xAxisIndex: 1,
        yAxisIndex: 1
      });
    }

    // RSI series
    if (chartConfig.indicators.rsi && rsiData.length > 0) {
      series.push({
        name: 'RSI',
        type: 'line',
        data: rsiData,
        smooth: true,
        lineStyle: { color: '#9c88ff', width: 2 },
        xAxisIndex: 2,
        yAxisIndex: 2
      });
    }

    return { dates, series };
  }, [ohlcData, chartConfig]);

  const chartOptions = useMemo(() => {
    if (!chartData) return {};

    const xAxes: any[] = [
      {
        type: 'category',
        data: chartData.dates,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' },
        splitLine: { show: false },
        gridIndex: 0
      }
    ];

    const yAxes: any[] = [
      {
        type: 'value',
        scale: true,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' },
        splitLine: { lineStyle: { color: '#333' } },
        gridIndex: 0
      }
    ];

    const grids: any[] = [
      {
        left: '10%',
        right: '8%',
        height: chartConfig.showVolume ? (chartConfig.indicators.rsi ? '40%' : '60%') : (chartConfig.indicators.rsi ? '60%' : '80%'),
        top: '10%'
      }
    ];

    let currentTop = chartConfig.showVolume ? (chartConfig.indicators.rsi ? '55%' : '75%') : (chartConfig.indicators.rsi ? '75%' : '95%');

    // Volume axis and grid
    if (chartConfig.showVolume) {
      xAxes.push({
        type: 'category',
        data: chartData.dates,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { show: false },
        splitLine: { show: false },
        gridIndex: 1
      });

      yAxes.push({
        type: 'value',
        scale: true,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { 
          color: '#999',
          formatter: (value: number) => formatVolume(value)
        },
        splitLine: { lineStyle: { color: '#333' } },
        gridIndex: 1
      });

      grids.push({
        left: '10%',
        right: '8%',
        height: chartConfig.indicators.rsi ? '15%' : '20%',
        top: currentTop
      });

      currentTop = chartConfig.indicators.rsi ? '75%' : '95%';
    }

    // RSI axis and grid
    if (chartConfig.indicators.rsi) {
      xAxes.push({
        type: 'category',
        data: chartData.dates,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' },
        splitLine: { show: false },
        gridIndex: chartConfig.showVolume ? 2 : 1
      });

      yAxes.push({
        type: 'value',
        scale: true,
        min: 0,
        max: 100,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' },
        splitLine: { 
          lineStyle: { color: '#333' },
          show: true
        },
        gridIndex: chartConfig.showVolume ? 2 : 1
      });

      grids.push({
        left: '10%',
        right: '8%',
        height: '20%',
        top: currentTop
      });
    }

    return {
      backgroundColor: 'transparent',
      animation: false,
      grid: grids,
      xAxis: xAxes,
      yAxis: yAxes,
      series: chartData.series,
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#2a2a2a',
        borderColor: '#666',
        textStyle: { color: '#fff' },
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          const date = params[0].axisValue;
          let tooltip = `<div style="margin-bottom: 5px; font-weight: bold;">${date}</div>`;
          
          params.forEach((param: any) => {
            if (param.seriesName === 'Bitcoin Price' && chartConfig.type === 'candlestick') {
              const [open, close, low, high] = param.data;
              tooltip += `
                <div>Open: $${open.toLocaleString()}</div>
                <div>High: $${high.toLocaleString()}</div>
                <div>Low: $${low.toLocaleString()}</div>
                <div>Close: $${close.toLocaleString()}</div>
              `;
            } else if (param.seriesName === 'Volume') {
              tooltip += `<div>Volume: ${formatVolume(param.data)}</div>`;
            } else if (param.seriesName === 'RSI') {
              tooltip += `<div>RSI: ${param.data.toFixed(2)}</div>`;
            } else if (param.seriesName.includes('SMA') || param.seriesName.includes('EMA')) {
              tooltip += `<div>${param.seriesName}: $${param.data.toLocaleString()}</div>`;
            } else if (param.seriesName === 'Bitcoin Price') {
              tooltip += `<div>Price: $${param.data.toLocaleString()}</div>`;
            }
          });
          
          return tooltip;
        }
      },
      legend: {
        data: chartData.series.map((s: any) => s.name),
        textStyle: { color: '#999' },
        top: 0
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1, 2],
          start: 70,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1, 2],
          type: 'slider',
          top: '95%',
          start: 70,
          end: 100,
          backgroundColor: '#2a2a2a',
          fillerColor: 'rgba(247, 147, 26, 0.2)',
          borderColor: '#666',
          handleStyle: { color: '#f7931a' },
          textStyle: { color: '#999' }
        }
      ]
    };
  }, [chartData, chartConfig]);

  if (loading) {
    return (
      <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <div style={{ height: height, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
          Loading chart data...
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <div style={{ height: height, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#ff6b6b' }}>
          Error: {error}
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* Chart Controls */}
      <Card className="mb-3" style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <div className="grid align-items-center">
          <div className="col-12 md:col-3">
            <label className="block text-sm font-medium mb-2" style={{ color: '#999' }}>Chart Type</label>
            <Dropdown
              value={chartConfig.type}
              options={chartTypeOptions}
              onChange={(e) => setChartConfig(prev => ({ ...prev, type: e.value }))}
              className="w-full"
            />
          </div>
          
          <div className="col-12 md:col-9">
            <label className="block text-sm font-medium mb-2" style={{ color: '#999' }}>Indicators</label>
            <div className="flex flex-wrap gap-3 align-items-center">
              <div className="flex align-items-center">
                <Checkbox
                  checked={chartConfig.showVolume}
                  onChange={(e) => setChartConfig(prev => ({ ...prev, showVolume: e.checked || false }))}
                />
                <label className="ml-2" style={{ color: '#999' }}>Volume</label>
              </div>

              <div className="flex align-items-center">
                <Checkbox
                  checked={chartConfig.indicators.rsi}
                  onChange={(e) => setChartConfig(prev => ({
                    ...prev,
                    indicators: { ...prev.indicators, rsi: e.checked || false }
                  }))}
                />
                <label className="ml-2" style={{ color: '#999' }}>RSI</label>
              </div>

              <div className="flex align-items-center gap-2">
                <Checkbox
                  checked={chartConfig.indicators.sma}
                  onChange={(e) => setChartConfig(prev => ({
                    ...prev,
                    indicators: { ...prev.indicators, sma: e.checked || false }
                  }))}
                />
                <label style={{ color: '#999' }}>SMA</label>
                <InputNumber
                  value={chartConfig.indicators.smaPeriod}
                  onValueChange={(e) => setChartConfig(prev => ({
                    ...prev,
                    indicators: { ...prev.indicators, smaPeriod: e.value || 20 }
                  }))}
                  min={5}
                  max={200}
                  style={{ width: '60px' }}
                />
              </div>

              <div className="flex align-items-center gap-2">
                <Checkbox
                  checked={chartConfig.indicators.ema}
                  onChange={(e) => setChartConfig(prev => ({
                    ...prev,
                    indicators: { ...prev.indicators, ema: e.checked || false }
                  }))}
                />
                <label style={{ color: '#999' }}>EMA</label>
                <InputNumber
                  value={chartConfig.indicators.emaPeriod}
                  onValueChange={(e) => setChartConfig(prev => ({
                    ...prev,
                    indicators: { ...prev.indicators, emaPeriod: e.value || 12 }
                  }))}
                  min={5}
                  max={200}
                  style={{ width: '60px' }}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Chart */}
      <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <ReactECharts
          option={chartOptions}
          style={{ height: height }}
          opts={{ renderer: 'canvas' }}
        />
      </Card>
    </div>
  );
};

export default AdvancedBitcoinChart;
