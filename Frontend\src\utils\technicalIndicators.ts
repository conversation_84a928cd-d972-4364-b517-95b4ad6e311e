import { OHLCData, RSIData, MovingAverageData } from '../types';

/**
 * Calculate Simple Moving Average (SMA)
 */
export const calculateSMA = (data: number[], period: number): number[] => {
  const sma: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      sma.push(NaN);
    } else {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val, 0);
      sma.push(sum / period);
    }
  }
  
  return sma;
};

/**
 * Calculate Exponential Moving Average (EMA)
 */
export const calculateEMA = (data: number[], period: number): number[] => {
  const ema: number[] = [];
  const multiplier = 2 / (period + 1);
  
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      ema.push(data[i]);
    } else {
      ema.push((data[i] * multiplier) + (ema[i - 1] * (1 - multiplier)));
    }
  }
  
  return ema;
};

/**
 * Calculate Relative Strength Index (RSI)
 */
export const calculateRSI = (prices: number[], period: number = 14): number[] => {
  const rsi: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];
  
  // Calculate price changes
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;
  
  // Fill initial values with NaN
  for (let i = 0; i <= period; i++) {
    rsi.push(NaN);
  }
  
  // Calculate RSI for remaining values
  for (let i = period; i < gains.length; i++) {
    avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
    avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;
    
    const rs = avgGain / avgLoss;
    const rsiValue = 100 - (100 / (1 + rs));
    rsi.push(rsiValue);
  }
  
  return rsi;
};

/**
 * Calculate Bollinger Bands
 */
export const calculateBollingerBands = (prices: number[], period: number = 20, stdDev: number = 2) => {
  const sma = calculateSMA(prices, period);
  const upperBand: number[] = [];
  const lowerBand: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      upperBand.push(NaN);
      lowerBand.push(NaN);
    } else {
      const slice = prices.slice(i - period + 1, i + 1);
      const mean = sma[i];
      const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
      const standardDeviation = Math.sqrt(variance);
      
      upperBand.push(mean + (standardDeviation * stdDev));
      lowerBand.push(mean - (standardDeviation * stdDev));
    }
  }
  
  return { sma, upperBand, lowerBand };
};

/**
 * Calculate MACD (Moving Average Convergence Divergence)
 */
export const calculateMACD = (prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) => {
  const fastEMA = calculateEMA(prices, fastPeriod);
  const slowEMA = calculateEMA(prices, slowPeriod);
  
  const macdLine: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    macdLine.push(fastEMA[i] - slowEMA[i]);
  }
  
  const signalLine = calculateEMA(macdLine, signalPeriod);
  const histogram: number[] = [];
  
  for (let i = 0; i < macdLine.length; i++) {
    histogram.push(macdLine[i] - signalLine[i]);
  }
  
  return { macdLine, signalLine, histogram };
};

/**
 * Parse volume string to number (handles K, M, B suffixes)
 */
export const parseVolume = (volumeStr: string): number => {
  if (typeof volumeStr !== 'string') return 0;
  
  const cleanStr = volumeStr.replace(/[",]/g, '');
  
  if (cleanStr.includes('K')) {
    return parseFloat(cleanStr.replace('K', '')) * 1000;
  } else if (cleanStr.includes('M')) {
    return parseFloat(cleanStr.replace('M', '')) * 1000000;
  } else if (cleanStr.includes('B')) {
    return parseFloat(cleanStr.replace('B', '')) * 1000000000;
  } else {
    return parseFloat(cleanStr) || 0;
  }
};

/**
 * Parse price string to number (removes commas and quotes)
 */
export const parsePrice = (priceStr: string): number => {
  if (typeof priceStr !== 'string') return 0;
  return parseFloat(priceStr.replace(/[",]/g, '')) || 0;
};

/**
 * Convert CSV data to OHLC format
 */
export const convertToOHLC = (csvData: any[]): OHLCData[] => {
  return csvData.map(row => ({
    date: row.Date,
    open: parsePrice(row.Open),
    high: parsePrice(row.High),
    low: parsePrice(row.Low),
    close: parsePrice(row.Price),
    volume: parseVolume(row['Vol.'] || row.Vol || '0'),
    changePercent: parseFloat((row['Change %'] || '0').replace('%', '')) || 0
  }));
};

/**
 * Generate trading signals based on technical indicators
 */
export const generateTradingSignals = (ohlcData: OHLCData[], rsiData: number[], smaData: number[], emaData: number[]) => {
  const signals: Array<{date: string, signal: 'buy' | 'sell' | 'hold', strength: number, reason: string}> = [];
  
  for (let i = 1; i < ohlcData.length; i++) {
    const current = ohlcData[i];
    const previous = ohlcData[i - 1];
    const rsi = rsiData[i];
    const sma = smaData[i];
    const ema = emaData[i];
    
    let signal: 'buy' | 'sell' | 'hold' = 'hold';
    let strength = 0;
    let reasons: string[] = [];
    
    // RSI signals
    if (!isNaN(rsi)) {
      if (rsi < 30) {
        signal = 'buy';
        strength += 0.3;
        reasons.push('RSI oversold');
      } else if (rsi > 70) {
        signal = 'sell';
        strength += 0.3;
        reasons.push('RSI overbought');
      }
    }
    
    // Moving average crossover
    if (!isNaN(sma) && !isNaN(ema)) {
      const prevSMA = smaData[i - 1];
      const prevEMA = emaData[i - 1];
      
      if (!isNaN(prevSMA) && !isNaN(prevEMA)) {
        // Golden cross (EMA crosses above SMA)
        if (ema > sma && prevEMA <= prevSMA) {
          signal = 'buy';
          strength += 0.4;
          reasons.push('Golden cross');
        }
        // Death cross (EMA crosses below SMA)
        else if (ema < sma && prevEMA >= prevSMA) {
          signal = 'sell';
          strength += 0.4;
          reasons.push('Death cross');
        }
      }
    }
    
    // Price momentum
    const priceChange = (current.close - previous.close) / previous.close;
    if (Math.abs(priceChange) > 0.02) { // 2% change
      if (priceChange > 0) {
        if (signal !== 'sell') {
          signal = 'buy';
          strength += 0.2;
          reasons.push('Strong upward momentum');
        }
      } else {
        if (signal !== 'buy') {
          signal = 'sell';
          strength += 0.2;
          reasons.push('Strong downward momentum');
        }
      }
    }
    
    signals.push({
      date: current.date,
      signal,
      strength: Math.min(strength, 1),
      reason: reasons.join(', ') || 'No clear signal'
    });
  }
  
  return signals;
};

/**
 * Format volume for display
 */
export const formatVolume = (volume: number): string => {
  if (volume >= 1e9) {
    return `${(volume / 1e9).toFixed(2)}B`;
  } else if (volume >= 1e6) {
    return `${(volume / 1e6).toFixed(2)}M`;
  } else if (volume >= 1e3) {
    return `${(volume / 1e3).toFixed(2)}K`;
  }
  return volume.toFixed(0);
};
