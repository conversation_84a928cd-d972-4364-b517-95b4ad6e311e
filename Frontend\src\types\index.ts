export interface PredictionResponse {
  [key: string]: {
    dates: string[];
    predicted_prices: number[];
  };
}

export type TimeFrame = '1m' | '6m' | '1y' | '3y' | 'custom';

export interface TimeFrameMapping {
  [key: string]: number;
}

export interface ChartData {
  dates: string[];
  prices: number[];
}

export interface DateRangeSelection {
  startDate: string;
  endDate: string;
}

export interface PredictionData {
  date: string;
  price: number;
}

// Advanced Charting Types
export interface OHLCData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  changePercent: number;
}

export interface TechnicalIndicator {
  name: string;
  values: number[];
  color: string;
  visible: boolean;
}

export interface RSIData {
  date: string;
  rsi: number;
}

export interface MovingAverageData {
  date: string;
  sma: number;
  ema: number;
}

export type ChartType = 'line' | 'area' | 'candlestick';

export interface ChartConfig {
  type: ChartType;
  showVolume: boolean;
  indicators: {
    rsi: boolean;
    sma: boolean;
    ema: boolean;
    smaPeriod: number;
    emaPeriod: number;
  };
}

// Portfolio Management Types
export interface PortfolioHolding {
  symbol: string;
  name: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  totalValue: number;
  unrealizedPL: number;
  unrealizedPLPercent: number;
  allocation: number;
}

export interface Transaction {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  total: number;
  timestamp: Date;
  fees: number;
}

export interface PortfolioSummary {
  totalValue: number;
  totalCost: number;
  totalUnrealizedPL: number;
  totalUnrealizedPLPercent: number;
  totalRealizedPL: number;
  benchmarkReturn: number;
  holdings: PortfolioHolding[];
  transactions: Transaction[];
}

export interface TradingSignal {
  symbol: string;
  type: 'buy' | 'sell' | 'hold';
  strength: 'weak' | 'moderate' | 'strong';
  reason: string;
  confidence: number;
  targetPrice?: number;
  stopLoss?: number;
}

// Performance Metrics Types
export interface ModelPerformanceMetrics {
  rmse: number;
  mae: number;
  mape: number;
  accuracy: number;
  directionalAccuracy: number;
  lastUpdated: Date;
  dataQuality: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
}

export interface BacktestResult {
  date: string;
  actualPrice: number;
  predictedPrice: number;
  error: number;
  absoluteError: number;
  percentageError: number;
}

export interface ModelReliability {
  overall: number;
  shortTerm: number;
  longTerm: number;
  volatilityAdjusted: number;
  trendAccuracy: number;
}