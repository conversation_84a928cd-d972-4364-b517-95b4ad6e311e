import { PortfolioHolding, Transaction, PortfolioSummary, TradingSignal } from '../types';
import { CryptoPriceInfo } from '../api/csvDataService';

export class PortfolioManager {
  private static STORAGE_KEY = 'bitcoin_portfolio';
  private static TRANSACTIONS_KEY = 'bitcoin_transactions';

  static savePortfolio(holdings: PortfolioHolding[]): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(holdings));
  }

  static loadPortfolio(): PortfolioHolding[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  static saveTransactions(transactions: Transaction[]): void {
    localStorage.setItem(this.TRANSACTIONS_KEY, JSON.stringify(transactions));
  }

  static loadTransactions(): Transaction[] {
    const stored = localStorage.getItem(this.TRANSACTIONS_KEY);
    return stored ? JSON.parse(stored).map((t: any) => ({
      ...t,
      timestamp: new Date(t.timestamp)
    })) : [];
  }

  static generateTransactionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  static executeTrade(
    symbol: string,
    name: string,
    type: 'buy' | 'sell',
    quantity: number,
    price: number,
    fees: number = 0
  ): { success: boolean; message: string; transaction?: Transaction } {
    const holdings = this.loadPortfolio();
    const transactions = this.loadTransactions();
    
    const existingHolding = holdings.find(h => h.symbol === symbol);
    const total = quantity * price + fees;

    if (type === 'sell') {
      if (!existingHolding || existingHolding.quantity < quantity) {
        return { success: false, message: 'Insufficient holdings to sell' };
      }
    }

    const transaction: Transaction = {
      id: this.generateTransactionId(),
      symbol,
      type,
      quantity,
      price,
      total: type === 'buy' ? total : quantity * price - fees,
      timestamp: new Date(),
      fees
    };

    // Update holdings
    if (type === 'buy') {
      if (existingHolding) {
        const newQuantity = existingHolding.quantity + quantity;
        const newAveragePrice = ((existingHolding.quantity * existingHolding.averagePrice) + total) / newQuantity;
        existingHolding.quantity = newQuantity;
        existingHolding.averagePrice = newAveragePrice;
      } else {
        holdings.push({
          symbol,
          name,
          quantity,
          averagePrice: price + (fees / quantity),
          currentPrice: price,
          totalValue: quantity * price,
          unrealizedPL: 0,
          unrealizedPLPercent: 0,
          allocation: 0
        });
      }
    } else { // sell
      if (existingHolding) {
        existingHolding.quantity -= quantity;
        if (existingHolding.quantity <= 0) {
          const index = holdings.findIndex(h => h.symbol === symbol);
          holdings.splice(index, 1);
        }
      }
    }

    transactions.push(transaction);
    this.savePortfolio(holdings);
    this.saveTransactions(transactions);

    return { 
      success: true, 
      message: `Successfully ${type === 'buy' ? 'bought' : 'sold'} ${quantity} ${symbol}`,
      transaction
    };
  }

  static updatePortfolioPrices(cryptoPrices: CryptoPriceInfo[]): PortfolioSummary {
    const holdings = this.loadPortfolio();
    const transactions = this.loadTransactions();
    
    let totalValue = 0;
    let totalCost = 0;
    let totalRealizedPL = 0;

    // Calculate realized P&L from completed sell transactions
    const sellTransactions = transactions.filter(t => t.type === 'sell');
    const buyTransactions = transactions.filter(t => t.type === 'buy');

    sellTransactions.forEach(sellTx => {
      const relevantBuys = buyTransactions
        .filter(buyTx => buyTx.symbol === sellTx.symbol && buyTx.timestamp <= sellTx.timestamp)
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()); // FIFO

      let remainingQuantity = sellTx.quantity;
      let totalBuyCost = 0;

      for (const buyTx of relevantBuys) {
        if (remainingQuantity <= 0) break;
        
        const quantityToUse = Math.min(remainingQuantity, buyTx.quantity);
        totalBuyCost += quantityToUse * buyTx.price;
        remainingQuantity -= quantityToUse;
      }

      totalRealizedPL += (sellTx.total - totalBuyCost);
    });

    // Update current holdings with latest prices
    const updatedHoldings = holdings.map(holding => {
      const priceInfo = cryptoPrices.find(p => p.symbol === holding.symbol);
      const currentPrice = priceInfo?.price || holding.currentPrice;
      const currentValue = holding.quantity * currentPrice;
      const totalCostForHolding = holding.quantity * holding.averagePrice;
      const unrealizedPL = currentValue - totalCostForHolding;
      const unrealizedPLPercent = totalCostForHolding > 0 ? (unrealizedPL / totalCostForHolding) * 100 : 0;

      totalValue += currentValue;
      totalCost += totalCostForHolding;

      return {
        ...holding,
        currentPrice,
        totalValue: currentValue,
        unrealizedPL,
        unrealizedPLPercent,
        allocation: 0 // Will be calculated after totalValue is known
      };
    });

    // Calculate allocations
    updatedHoldings.forEach(holding => {
      holding.allocation = totalValue > 0 ? (holding.totalValue / totalValue) * 100 : 0;
    });

    // Calculate benchmark return (Bitcoin)
    const btcPrice = cryptoPrices.find(p => p.symbol === 'BTC');
    const benchmarkReturn = btcPrice?.changePercent24h || 0;

    const totalUnrealizedPL = totalValue - totalCost;
    const totalUnrealizedPLPercent = totalCost > 0 ? (totalUnrealizedPL / totalCost) * 100 : 0;

    this.savePortfolio(updatedHoldings);

    return {
      totalValue,
      totalCost,
      totalUnrealizedPL,
      totalUnrealizedPLPercent,
      totalRealizedPL,
      benchmarkReturn,
      holdings: updatedHoldings,
      transactions
    };
  }

  static generateTradingSignals(
    cryptoPrices: CryptoPriceInfo[],
    technicalData?: any
  ): TradingSignal[] {
    const signals: TradingSignal[] = [];

    cryptoPrices.forEach(crypto => {
      let signal: 'buy' | 'sell' | 'hold' = 'hold';
      let strength: 'weak' | 'moderate' | 'strong' = 'weak';
      let confidence = 0;
      let reason = '';

      // Price momentum analysis
      const change24h = crypto.changePercent24h;
      const change7d = crypto.changePercent7d;

      // Strong upward momentum
      if (change24h > 5 && change7d > 10) {
        signal = 'buy';
        strength = 'strong';
        confidence = 0.8;
        reason = 'Strong upward momentum (24h: +' + change24h.toFixed(2) + '%, 7d: +' + change7d.toFixed(2) + '%)';
      }
      // Moderate upward momentum
      else if (change24h > 2 && change7d > 5) {
        signal = 'buy';
        strength = 'moderate';
        confidence = 0.6;
        reason = 'Moderate upward momentum';
      }
      // Weak upward momentum
      else if (change24h > 0 && change7d > 0) {
        signal = 'buy';
        strength = 'weak';
        confidence = 0.4;
        reason = 'Weak upward momentum';
      }
      // Strong downward momentum
      else if (change24h < -5 && change7d < -10) {
        signal = 'sell';
        strength = 'strong';
        confidence = 0.8;
        reason = 'Strong downward momentum (24h: ' + change24h.toFixed(2) + '%, 7d: ' + change7d.toFixed(2) + '%)';
      }
      // Moderate downward momentum
      else if (change24h < -2 && change7d < -5) {
        signal = 'sell';
        strength = 'moderate';
        confidence = 0.6;
        reason = 'Moderate downward momentum';
      }
      // Oversold condition (potential buy opportunity)
      else if (change24h < -10) {
        signal = 'buy';
        strength = 'moderate';
        confidence = 0.5;
        reason = 'Potential oversold condition';
      }
      // Overbought condition (potential sell opportunity)
      else if (change24h > 15) {
        signal = 'sell';
        strength = 'moderate';
        confidence = 0.5;
        reason = 'Potential overbought condition';
      }
      else {
        reason = 'No clear trend, sideways movement';
      }

      // Adjust confidence based on volatility
      const volatility = Math.abs(change24h - change7d / 7);
      if (volatility > 5) {
        confidence *= 0.8; // Reduce confidence in high volatility
        reason += ' (High volatility detected)';
      }

      signals.push({
        symbol: crypto.symbol,
        type: signal,
        strength,
        reason,
        confidence,
        targetPrice: signal === 'buy' ? crypto.price * 1.1 : crypto.price * 0.9,
        stopLoss: signal === 'buy' ? crypto.price * 0.95 : crypto.price * 1.05
      });
    });

    return signals;
  }

  static calculatePortfolioMetrics(summary: PortfolioSummary): {
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
    avgWin: number;
    avgLoss: number;
  } {
    const transactions = summary.transactions;
    const sellTransactions = transactions.filter(t => t.type === 'sell');
    
    if (sellTransactions.length === 0) {
      return {
        sharpeRatio: 0,
        maxDrawdown: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0
      };
    }

    // Calculate individual trade P&L
    const tradePLs: number[] = [];
    const buyTransactions = transactions.filter(t => t.type === 'buy');

    sellTransactions.forEach(sellTx => {
      const relevantBuys = buyTransactions
        .filter(buyTx => buyTx.symbol === sellTx.symbol && buyTx.timestamp <= sellTx.timestamp)
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      let remainingQuantity = sellTx.quantity;
      let totalBuyCost = 0;

      for (const buyTx of relevantBuys) {
        if (remainingQuantity <= 0) break;
        const quantityToUse = Math.min(remainingQuantity, buyTx.quantity);
        totalBuyCost += quantityToUse * buyTx.price;
        remainingQuantity -= quantityToUse;
      }

      const tradePL = sellTx.total - totalBuyCost;
      tradePLs.push(tradePL);
    });

    const wins = tradePLs.filter(pl => pl > 0);
    const losses = tradePLs.filter(pl => pl < 0);
    
    const winRate = tradePLs.length > 0 ? (wins.length / tradePLs.length) * 100 : 0;
    const avgWin = wins.length > 0 ? wins.reduce((sum, win) => sum + win, 0) / wins.length : 0;
    const avgLoss = losses.length > 0 ? Math.abs(losses.reduce((sum, loss) => sum + loss, 0) / losses.length) : 0;

    // Simple Sharpe ratio approximation
    const avgReturn = tradePLs.length > 0 ? tradePLs.reduce((sum, pl) => sum + pl, 0) / tradePLs.length : 0;
    const returnStdDev = tradePLs.length > 1 ? Math.sqrt(
      tradePLs.reduce((sum, pl) => sum + Math.pow(pl - avgReturn, 2), 0) / (tradePLs.length - 1)
    ) : 0;
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    // Simple max drawdown calculation
    let peak = 0;
    let maxDrawdown = 0;
    let runningPL = 0;

    tradePLs.forEach(pl => {
      runningPL += pl;
      if (runningPL > peak) {
        peak = runningPL;
      }
      const drawdown = (peak - runningPL) / Math.max(peak, 1);
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    });

    return {
      sharpeRatio,
      maxDrawdown: maxDrawdown * 100,
      winRate,
      avgWin,
      avgLoss
    };
  }

  static clearPortfolio(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.TRANSACTIONS_KEY);
  }
}
