import React, { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { Badge } from 'primereact/badge';
import { Toast } from 'primereact/toast';
import { TabView, TabPanel } from 'primereact/tabview';
import { Chart } from 'primereact/chart';
import { ProgressBar } from 'primereact/progressbar';
import { fetchAllCryptoPrices, CryptoPriceInfo } from '../api/csvDataService';
import { PortfolioSummary, TradingSignal, Transaction } from '../types';
import { PortfolioManager } from '../utils/portfolioManager';

interface PortfolioManagerProps {}

const PortfolioManagerComponent: React.FC<PortfolioManagerProps> = () => {
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary | null>(null);
  const [cryptoPrices, setCryptoPrices] = useState<CryptoPriceInfo[]>([]);
  const [tradingSignals, setTradingSignals] = useState<TradingSignal[]>([]);
  const [loading, setLoading] = useState(true);
  const [showTradeDialog, setShowTradeDialog] = useState(false);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [tradeType, setTradeType] = useState<'buy' | 'sell'>('buy');
  const [tradeQuantity, setTradeQuantity] = useState<number>(0);
  const [tradePrice, setTradePrice] = useState<number>(0);
  const toast = React.useRef<Toast>(null);

  const cryptoOptions = [
    { label: 'Bitcoin (BTC)', value: 'BTC' },
    { label: 'Ethereum (ETH)', value: 'ETH' },
    { label: 'XRP (XRP)', value: 'XRP' },
    { label: 'Solana (SOL)', value: 'SOL' },
    { label: 'BNB (BNB)', value: 'BNB' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const prices = await fetchAllCryptoPrices();
      setCryptoPrices(prices);
      
      const summary = PortfolioManager.updatePortfolioPrices(prices);
      setPortfolioSummary(summary);
      
      const signals = PortfolioManager.generateTradingSignals(prices);
      setTradingSignals(signals);
    } catch (error) {
      console.error('Error loading portfolio data:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load portfolio data'
      });
    } finally {
      setLoading(false);
    }
  };

  const openTradeDialog = (symbol?: string, type?: 'buy' | 'sell') => {
    if (symbol) {
      setSelectedCrypto(symbol);
      const crypto = cryptoPrices.find(c => c.symbol === symbol);
      if (crypto) {
        setTradePrice(crypto.price);
      }
    }
    if (type) {
      setTradeType(type);
    }
    setTradeQuantity(0);
    setShowTradeDialog(true);
  };

  const executeTrade = () => {
    if (!selectedCrypto || tradeQuantity <= 0 || tradePrice <= 0) {
      toast.current?.show({
        severity: 'warn',
        summary: 'Invalid Trade',
        detail: 'Please fill in all trade details'
      });
      return;
    }

    const crypto = cryptoPrices.find(c => c.symbol === selectedCrypto);
    if (!crypto) return;

    const result = PortfolioManager.executeTrade(
      selectedCrypto,
      crypto.name,
      tradeType,
      tradeQuantity,
      tradePrice,
      0 // No fees for simulation
    );

    if (result.success) {
      toast.current?.show({
        severity: 'success',
        summary: 'Trade Executed',
        detail: result.message
      });
      setShowTradeDialog(false);
      loadData(); // Refresh data
    } else {
      toast.current?.show({
        severity: 'error',
        summary: 'Trade Failed',
        detail: result.message
      });
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const holdingNameTemplate = (rowData: any) => {
    return (
      <div className="flex align-items-center gap-2">
        <i className="pi pi-circle-fill text-orange-500"></i>
        <div>
          <div className="font-semibold">{rowData.name}</div>
          <div className="text-sm text-500">{rowData.symbol}</div>
        </div>
      </div>
    );
  };

  const plTemplate = (rowData: any) => {
    const isPositive = rowData.unrealizedPL >= 0;
    return (
      <div>
        <div className={isPositive ? 'text-green-500' : 'text-red-500'}>
          {formatCurrency(rowData.unrealizedPL)}
        </div>
        <div className={`text-sm ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
          {formatPercent(rowData.unrealizedPLPercent)}
        </div>
      </div>
    );
  };

  const allocationTemplate = (rowData: any) => {
    return (
      <div>
        <div>{rowData.allocation.toFixed(1)}%</div>
        <ProgressBar 
          value={rowData.allocation} 
          style={{ height: '6px' }}
          color="#f7931a"
        />
      </div>
    );
  };

  const signalTemplate = (rowData: TradingSignal) => {
    const getSignalColor = (type: string) => {
      switch (type) {
        case 'buy': return 'success';
        case 'sell': return 'danger';
        default: return 'info';
      }
    };

    const getStrengthColor = (strength: string) => {
      switch (strength) {
        case 'strong': return '#ff6b6b';
        case 'moderate': return '#ffa726';
        default: return '#66bb6a';
      }
    };

    return (
      <div>
        <Badge 
          value={rowData.type.toUpperCase()} 
          severity={getSignalColor(rowData.type)}
          className="mb-1"
        />
        <div className="text-sm" style={{ color: getStrengthColor(rowData.strength) }}>
          {rowData.strength} ({(rowData.confidence * 100).toFixed(0)}%)
        </div>
      </div>
    );
  };

  const actionTemplate = (rowData: TradingSignal) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-plus"
          size="small"
          severity="success"
          tooltip="Buy"
          onClick={() => openTradeDialog(rowData.symbol, 'buy')}
        />
        <Button
          icon="pi pi-minus"
          size="small"
          severity="danger"
          tooltip="Sell"
          onClick={() => openTradeDialog(rowData.symbol, 'sell')}
        />
      </div>
    );
  };

  const transactionTypeTemplate = (rowData: Transaction) => {
    return (
      <Badge 
        value={rowData.type.toUpperCase()} 
        severity={rowData.type === 'buy' ? 'success' : 'danger'}
      />
    );
  };

  const getPortfolioChartData = () => {
    if (!portfolioSummary || portfolioSummary.holdings.length === 0) return null;

    return {
      labels: portfolioSummary.holdings.map(h => h.symbol),
      datasets: [{
        data: portfolioSummary.holdings.map(h => h.allocation),
        backgroundColor: [
          '#f7931a', // Bitcoin orange
          '#627eea', // Ethereum blue
          '#23292f', // XRP black
          '#9945ff', // Solana purple
          '#f3ba2f'  // BNB yellow
        ],
        borderWidth: 2,
        borderColor: '#2a2a2a'
      }]
    };
  };

  const chartOptions = {
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          color: '#999',
          usePointStyle: true
        }
      }
    },
    maintainAspectRatio: false
  };

  if (loading) {
    return (
      <div className="p-4">
        <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
          <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
            Loading portfolio data...
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4" style={{ backgroundColor: '#1a1a1a', minHeight: '100vh', color: '#ffffff' }}>
      <Toast ref={toast} />
      
      {/* Portfolio Summary Cards */}
      <div className="grid mb-4">
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Total Value</div>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(portfolioSummary?.totalValue || 0)}
            </div>
          </Card>
        </div>
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Total P&L</div>
            <div className={`text-2xl font-bold ${(portfolioSummary?.totalUnrealizedPL || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {formatCurrency(portfolioSummary?.totalUnrealizedPL || 0)}
            </div>
            <div className={`text-sm ${(portfolioSummary?.totalUnrealizedPLPercent || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(portfolioSummary?.totalUnrealizedPLPercent || 0)}
            </div>
          </Card>
        </div>
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Realized P&L</div>
            <div className={`text-2xl font-bold ${(portfolioSummary?.totalRealizedPL || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {formatCurrency(portfolioSummary?.totalRealizedPL || 0)}
            </div>
          </Card>
        </div>
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">vs BTC Benchmark</div>
            <div className={`text-2xl font-bold ${(portfolioSummary?.benchmarkReturn || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {formatPercent(portfolioSummary?.benchmarkReturn || 0)}
            </div>
          </Card>
        </div>
      </div>

      <div className="grid">
        {/* Portfolio Allocation Chart */}
        <div className="col-12 md:col-4">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
            <h3 className="text-white mb-3">Portfolio Allocation</h3>
            {getPortfolioChartData() ? (
              <Chart 
                type="doughnut" 
                data={getPortfolioChartData()!} 
                options={chartOptions}
                style={{ height: '300px' }}
              />
            ) : (
              <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#666' }}>
                No holdings to display
              </div>
            )}
          </Card>
        </div>

        {/* Main Content */}
        <div className="col-12 md:col-8">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
            <div className="flex justify-content-between align-items-center mb-3">
              <h3 className="text-white m-0">Portfolio Management</h3>
              <Button
                label="New Trade"
                icon="pi pi-plus"
                onClick={() => openTradeDialog()}
                className="p-button-success"
              />
            </div>
            
            <TabView>
              <TabPanel header="Holdings" leftIcon="pi pi-wallet">
                <DataTable 
                  value={portfolioSummary?.holdings || []} 
                  className="p-datatable-sm"
                  emptyMessage="No holdings found. Start by making your first trade!"
                >
                  <Column field="name" header="Asset" body={holdingNameTemplate} />
                  <Column field="quantity" header="Quantity" body={(data) => data.quantity.toFixed(6)} />
                  <Column field="averagePrice" header="Avg Price" body={(data) => formatCurrency(data.averagePrice)} />
                  <Column field="currentPrice" header="Current Price" body={(data) => formatCurrency(data.currentPrice)} />
                  <Column field="totalValue" header="Value" body={(data) => formatCurrency(data.totalValue)} />
                  <Column field="unrealizedPL" header="P&L" body={plTemplate} />
                  <Column field="allocation" header="Allocation" body={allocationTemplate} />
                </DataTable>
              </TabPanel>
              
              <TabPanel header="Trading Signals" leftIcon="pi pi-chart-line">
                <DataTable 
                  value={tradingSignals} 
                  className="p-datatable-sm"
                >
                  <Column field="symbol" header="Symbol" />
                  <Column field="type" header="Signal" body={signalTemplate} />
                  <Column field="reason" header="Reason" />
                  <Column field="targetPrice" header="Target" body={(data) => formatCurrency(data.targetPrice || 0)} />
                  <Column header="Actions" body={actionTemplate} />
                </DataTable>
              </TabPanel>
              
              <TabPanel header="Transaction History" leftIcon="pi pi-history">
                <DataTable 
                  value={portfolioSummary?.transactions || []} 
                  className="p-datatable-sm"
                  emptyMessage="No transactions yet"
                >
                  <Column field="timestamp" header="Date" body={(data) => new Date(data.timestamp).toLocaleDateString()} />
                  <Column field="symbol" header="Symbol" />
                  <Column field="type" header="Type" body={transactionTypeTemplate} />
                  <Column field="quantity" header="Quantity" body={(data) => data.quantity.toFixed(6)} />
                  <Column field="price" header="Price" body={(data) => formatCurrency(data.price)} />
                  <Column field="total" header="Total" body={(data) => formatCurrency(data.total)} />
                </DataTable>
              </TabPanel>
            </TabView>
          </Card>
        </div>
      </div>

      {/* Trade Dialog */}
      <Dialog
        header={`${tradeType === 'buy' ? 'Buy' : 'Sell'} Cryptocurrency`}
        visible={showTradeDialog}
        onHide={() => setShowTradeDialog(false)}
        style={{ width: '400px' }}
        contentStyle={{ backgroundColor: '#2a2a2a', color: '#fff' }}
        headerStyle={{ backgroundColor: '#2a2a2a', color: '#fff', borderBottom: '1px solid #404040' }}
      >
        <div className="grid">
          <div className="col-12">
            <label className="block text-sm font-medium mb-2">Cryptocurrency</label>
            <Dropdown
              value={selectedCrypto}
              options={cryptoOptions}
              onChange={(e) => {
                setSelectedCrypto(e.value);
                const crypto = cryptoPrices.find(c => c.symbol === e.value);
                if (crypto) setTradePrice(crypto.price);
              }}
              placeholder="Select cryptocurrency"
              className="w-full"
            />
          </div>
          <div className="col-12">
            <label className="block text-sm font-medium mb-2">Trade Type</label>
            <Dropdown
              value={tradeType}
              options={[
                { label: 'Buy', value: 'buy' },
                { label: 'Sell', value: 'sell' }
              ]}
              onChange={(e) => setTradeType(e.value)}
              className="w-full"
            />
          </div>
          <div className="col-12">
            <label className="block text-sm font-medium mb-2">Quantity</label>
            <InputNumber
              value={tradeQuantity}
              onValueChange={(e) => setTradeQuantity(e.value || 0)}
              min={0}
              maxFractionDigits={6}
              className="w-full"
            />
          </div>
          <div className="col-12">
            <label className="block text-sm font-medium mb-2">Price (USD)</label>
            <InputNumber
              value={tradePrice}
              onValueChange={(e) => setTradePrice(e.value || 0)}
              min={0}
              maxFractionDigits={2}
              className="w-full"
            />
          </div>
          <div className="col-12">
            <div className="text-sm text-500 mb-3">
              Total: {formatCurrency((tradeQuantity || 0) * (tradePrice || 0))}
            </div>
            <div className="flex gap-2">
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => setShowTradeDialog(false)}
                className="p-button-secondary flex-1"
              />
              <Button
                label={`${tradeType === 'buy' ? 'Buy' : 'Sell'}`}
                icon={`pi pi-${tradeType === 'buy' ? 'plus' : 'minus'}`}
                onClick={executeTrade}
                className={`flex-1 ${tradeType === 'buy' ? 'p-button-success' : 'p-button-danger'}`}
              />
            </div>
          </div>
        </div>
      </Dialog>
    </div>
  );
};

export default PortfolioManagerComponent;
