import { ModelPerformanceMetrics, BacktestResult, ModelReliability } from '../types';
import { loadPredictionData, fetchBitcoinData } from '../api/csvDataService';
import { convertToOHLC } from './technicalIndicators';

export class PerformanceAnalyzer {
  /**
   * Calculate Root Mean Square Error
   */
  static calculateRMSE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length || actual.length === 0) return 0;
    
    const sumSquaredErrors = actual.reduce((sum, actualValue, index) => {
      const error = actualValue - predicted[index];
      return sum + (error * error);
    }, 0);
    
    return Math.sqrt(sumSquaredErrors / actual.length);
  }

  /**
   * Calculate Mean Absolute Error
   */
  static calculateMAE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length || actual.length === 0) return 0;
    
    const sumAbsoluteErrors = actual.reduce((sum, actualValue, index) => {
      return sum + Math.abs(actualValue - predicted[index]);
    }, 0);
    
    return sumAbsoluteErrors / actual.length;
  }

  /**
   * Calculate Mean Absolute Percentage Error
   */
  static calculateMAPE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length || actual.length === 0) return 0;
    
    const sumPercentageErrors = actual.reduce((sum, actualValue, index) => {
      if (actualValue === 0) return sum;
      const percentageError = Math.abs((actualValue - predicted[index]) / actualValue);
      return sum + percentageError;
    }, 0);
    
    return (sumPercentageErrors / actual.length) * 100;
  }

  /**
   * Calculate directional accuracy (how often the model predicts the correct direction)
   */
  static calculateDirectionalAccuracy(actual: number[], predicted: number[]): number {
    if (actual.length < 2 || predicted.length < 2) return 0;
    
    let correctDirections = 0;
    let totalDirections = 0;
    
    for (let i = 1; i < actual.length; i++) {
      const actualDirection = actual[i] > actual[i - 1];
      const predictedDirection = predicted[i] > predicted[i - 1];
      
      if (actualDirection === predictedDirection) {
        correctDirections++;
      }
      totalDirections++;
    }
    
    return totalDirections > 0 ? (correctDirections / totalDirections) * 100 : 0;
  }

  /**
   * Calculate confidence intervals using standard deviation
   */
  static calculateConfidenceInterval(
    actual: number[], 
    predicted: number[], 
    confidenceLevel: number = 0.95
  ): { lower: number; upper: number } {
    if (actual.length !== predicted.length || actual.length === 0) {
      return { lower: 0, upper: 0 };
    }
    
    const errors = actual.map((actualValue, index) => actualValue - predicted[index]);
    const meanError = errors.reduce((sum, error) => sum + error, 0) / errors.length;
    
    const variance = errors.reduce((sum, error) => {
      return sum + Math.pow(error - meanError, 2);
    }, 0) / (errors.length - 1);
    
    const standardError = Math.sqrt(variance);
    
    // Using t-distribution approximation for 95% confidence
    const tValue = confidenceLevel === 0.95 ? 1.96 : 2.58; // 95% or 99%
    
    return {
      lower: meanError - (tValue * standardError),
      upper: meanError + (tValue * standardError)
    };
  }

  /**
   * Generate synthetic historical data for backtesting demonstration
   * In a real scenario, this would use actual historical predictions vs outcomes
   */
  static async generateBacktestData(): Promise<BacktestResult[]> {
    try {
      const historicalData = await fetchBitcoinData();
      const ohlcData = convertToOHLC(historicalData);
      
      // Take last 100 days for backtesting simulation
      const recentData = ohlcData.slice(-100);
      
      return recentData.map((data, index) => {
        // Simulate prediction with some realistic error
        const basePrice = data.close;
        const randomError = (Math.random() - 0.5) * 0.1; // ±5% random error
        const trendError = Math.sin(index * 0.1) * 0.05; // Systematic trend error
        const predictedPrice = basePrice * (1 + randomError + trendError);
        
        const error = data.close - predictedPrice;
        const absoluteError = Math.abs(error);
        const percentageError = Math.abs(error / data.close) * 100;
        
        return {
          date: data.date,
          actualPrice: data.close,
          predictedPrice,
          error,
          absoluteError,
          percentageError
        };
      });
    } catch (error) {
      console.error('Error generating backtest data:', error);
      return [];
    }
  }

  /**
   * Calculate comprehensive model performance metrics
   */
  static async calculateModelPerformance(): Promise<ModelPerformanceMetrics> {
    try {
      const backtestData = await this.generateBacktestData();
      
      if (backtestData.length === 0) {
        return {
          rmse: 0,
          mae: 0,
          mape: 0,
          accuracy: 0,
          directionalAccuracy: 0,
          lastUpdated: new Date(),
          dataQuality: 0,
          confidenceInterval: { lower: 0, upper: 0 }
        };
      }
      
      const actualPrices = backtestData.map(d => d.actualPrice);
      const predictedPrices = backtestData.map(d => d.predictedPrice);
      
      const rmse = this.calculateRMSE(actualPrices, predictedPrices);
      const mae = this.calculateMAE(actualPrices, predictedPrices);
      const mape = this.calculateMAPE(actualPrices, predictedPrices);
      const directionalAccuracy = this.calculateDirectionalAccuracy(actualPrices, predictedPrices);
      const confidenceInterval = this.calculateConfidenceInterval(actualPrices, predictedPrices);
      
      // Calculate overall accuracy (inverse of MAPE, capped at 100%)
      const accuracy = Math.max(0, Math.min(100, 100 - mape));
      
      // Calculate data quality score based on various factors
      const dataQuality = this.calculateDataQuality(backtestData);
      
      return {
        rmse,
        mae,
        mape,
        accuracy,
        directionalAccuracy,
        lastUpdated: new Date(),
        dataQuality,
        confidenceInterval
      };
    } catch (error) {
      console.error('Error calculating model performance:', error);
      return {
        rmse: 0,
        mae: 0,
        mape: 0,
        accuracy: 0,
        directionalAccuracy: 0,
        lastUpdated: new Date(),
        dataQuality: 0,
        confidenceInterval: { lower: 0, upper: 0 }
      };
    }
  }

  /**
   * Calculate data quality score
   */
  static calculateDataQuality(backtestData: BacktestResult[]): number {
    if (backtestData.length === 0) return 0;
    
    // Factors that affect data quality:
    // 1. Consistency of errors (lower variance = higher quality)
    // 2. Absence of extreme outliers
    // 3. Sufficient data points
    
    const errors = backtestData.map(d => d.percentageError);
    const meanError = errors.reduce((sum, error) => sum + error, 0) / errors.length;
    
    const errorVariance = errors.reduce((sum, error) => {
      return sum + Math.pow(error - meanError, 2);
    }, 0) / errors.length;
    
    const errorStdDev = Math.sqrt(errorVariance);
    
    // Count outliers (errors > 2 standard deviations)
    const outliers = errors.filter(error => Math.abs(error - meanError) > 2 * errorStdDev);
    const outlierRatio = outliers.length / errors.length;
    
    // Data quality score (0-100)
    let qualityScore = 100;
    
    // Penalize high error variance
    qualityScore -= Math.min(50, errorStdDev * 2);
    
    // Penalize high outlier ratio
    qualityScore -= outlierRatio * 30;
    
    // Penalize insufficient data
    if (backtestData.length < 30) {
      qualityScore -= (30 - backtestData.length) * 2;
    }
    
    return Math.max(0, Math.min(100, qualityScore));
  }

  /**
   * Calculate model reliability across different timeframes
   */
  static calculateModelReliability(backtestData: BacktestResult[]): ModelReliability {
    if (backtestData.length === 0) {
      return {
        overall: 0,
        shortTerm: 0,
        longTerm: 0,
        volatilityAdjusted: 0,
        trendAccuracy: 0
      };
    }
    
    const errors = backtestData.map(d => d.percentageError);
    const actualPrices = backtestData.map(d => d.actualPrice);
    const predictedPrices = backtestData.map(d => d.predictedPrice);
    
    // Overall reliability (inverse of mean error)
    const meanError = errors.reduce((sum, error) => sum + error, 0) / errors.length;
    const overall = Math.max(0, 100 - meanError);
    
    // Short-term reliability (last 30 days)
    const shortTermData = backtestData.slice(-30);
    const shortTermErrors = shortTermData.map(d => d.percentageError);
    const shortTermMeanError = shortTermErrors.reduce((sum, error) => sum + error, 0) / shortTermErrors.length;
    const shortTerm = Math.max(0, 100 - shortTermMeanError);
    
    // Long-term reliability (first 30 days vs last 30 days consistency)
    const longTermData = backtestData.slice(0, 30);
    const longTermErrors = longTermData.map(d => d.percentageError);
    const longTermMeanError = longTermErrors.reduce((sum, error) => sum + error, 0) / longTermErrors.length;
    const longTerm = Math.max(0, 100 - Math.abs(shortTermMeanError - longTermMeanError));
    
    // Volatility-adjusted reliability
    const priceVolatility = this.calculateVolatility(actualPrices);
    const volatilityAdjusted = overall * (1 - Math.min(0.5, priceVolatility / 100));
    
    // Trend accuracy
    const trendAccuracy = this.calculateDirectionalAccuracy(actualPrices, predictedPrices);
    
    return {
      overall,
      shortTerm,
      longTerm,
      volatilityAdjusted,
      trendAccuracy
    };
  }

  /**
   * Calculate price volatility
   */
  static calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    
    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => {
      return sum + Math.pow(ret - meanReturn, 2);
    }, 0) / returns.length;
    
    return Math.sqrt(variance) * 100; // Convert to percentage
  }

  /**
   * Generate performance trend data for charts
   */
  static generatePerformanceTrend(backtestData: BacktestResult[]): {
    dates: string[];
    accuracy: number[];
    errors: number[];
  } {
    const windowSize = 10; // Rolling window for trend calculation
    const dates: string[] = [];
    const accuracy: number[] = [];
    const errors: number[] = [];
    
    for (let i = windowSize - 1; i < backtestData.length; i++) {
      const window = backtestData.slice(i - windowSize + 1, i + 1);
      const windowErrors = window.map(d => d.percentageError);
      const meanError = windowErrors.reduce((sum, error) => sum + error, 0) / windowErrors.length;
      const windowAccuracy = Math.max(0, 100 - meanError);
      
      dates.push(backtestData[i].date);
      accuracy.push(windowAccuracy);
      errors.push(meanError);
    }
    
    return { dates, accuracy, errors };
  }
}
