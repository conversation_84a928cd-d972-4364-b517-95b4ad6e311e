import React, { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { Tab<PERSON>iew, Tab<PERSON>anel } from 'primereact/tabview';
import { ProgressBar } from 'primereact/progressbar';
import { Badge } from 'primereact/badge';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Skeleton } from 'primereact/skeleton';
import ReactECharts from 'echarts-for-react';
import { ModelPerformanceMetrics, BacktestResult, ModelReliability } from '../types';
import { PerformanceAnalyzer } from '../utils/performanceMetrics';

interface ModelPerformanceProps {}

const ModelPerformance: React.FC<ModelPerformanceProps> = () => {
  const [performanceMetrics, setPerformanceMetrics] = useState<ModelPerformanceMetrics | null>(null);
  const [backtestData, setBacktestData] = useState<BacktestResult[]>([]);
  const [modelReliability, setModelReliability] = useState<ModelReliability | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load performance metrics
      const metrics = await PerformanceAnalyzer.calculateModelPerformance();
      setPerformanceMetrics(metrics);

      // Load backtest data
      const backtest = await PerformanceAnalyzer.generateBacktestData();
      setBacktestData(backtest);

      // Calculate model reliability
      const reliability = PerformanceAnalyzer.calculateModelReliability(backtest);
      setModelReliability(reliability);

    } catch (err: any) {
      console.error('Error loading performance data:', err);
      setError(err.message || 'Failed to load performance data');
    } finally {
      setLoading(false);
    }
  };

  const getMetricColor = (value: number, type: 'accuracy' | 'error'): string => {
    if (type === 'accuracy') {
      if (value >= 80) return '#00da3c';
      if (value >= 60) return '#ffa726';
      return '#ec0000';
    } else {
      if (value <= 5) return '#00da3c';
      if (value <= 15) return '#ffa726';
      return '#ec0000';
    }
  };

  const getReliabilityBadge = (value: number): { severity: any; label: string } => {
    if (value >= 80) return { severity: 'success', label: 'Excellent' };
    if (value >= 60) return { severity: 'warning', label: 'Good' };
    if (value >= 40) return { severity: 'info', label: 'Fair' };
    return { severity: 'danger', label: 'Poor' };
  };

  const getActualVsPredictedChartOptions = () => {
    if (!backtestData || backtestData.length === 0) return {};

    const dates = backtestData.map(d => d.date);
    const actualPrices = backtestData.map(d => d.actualPrice);
    const predictedPrices = backtestData.map(d => d.predictedPrice);

    return {
      backgroundColor: 'transparent',
      title: {
        text: 'Actual vs Predicted Prices',
        textStyle: { color: '#fff' },
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#2a2a2a',
        borderColor: '#666',
        textStyle: { color: '#fff' },
        formatter: function(params: any) {
          const date = params[0].axisValue;
          const actual = params[0].data;
          const predicted = params[1].data;
          const error = Math.abs(actual - predicted);
          const errorPercent = ((error / actual) * 100).toFixed(2);
          
          return `
            <div style="margin-bottom: 5px; font-weight: bold;">${date}</div>
            <div>Actual: $${actual.toLocaleString()}</div>
            <div>Predicted: $${predicted.toLocaleString()}</div>
            <div>Error: $${error.toLocaleString()} (${errorPercent}%)</div>
          `;
        }
      },
      legend: {
        data: ['Actual Price', 'Predicted Price'],
        textStyle: { color: '#999' },
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { 
          color: '#999',
          formatter: (value: number) => `$${(value / 1000).toFixed(0)}K`
        },
        splitLine: { lineStyle: { color: '#333' } }
      },
      series: [
        {
          name: 'Actual Price',
          type: 'line',
          data: actualPrices,
          smooth: true,
          lineStyle: { color: '#00da3c', width: 2 },
          itemStyle: { color: '#00da3c' }
        },
        {
          name: 'Predicted Price',
          type: 'line',
          data: predictedPrices,
          smooth: true,
          lineStyle: { color: '#f7931a', width: 2, type: 'dashed' },
          itemStyle: { color: '#f7931a' }
        }
      ]
    };
  };

  const getErrorDistributionChartOptions = () => {
    if (!backtestData || backtestData.length === 0) return {};

    // Create error distribution histogram
    const errors = backtestData.map(d => d.percentageError);
    const bins = 20;
    const minError = Math.min(...errors);
    const maxError = Math.max(...errors);
    const binSize = (maxError - minError) / bins;
    
    const histogram = new Array(bins).fill(0);
    const binLabels = [];
    
    for (let i = 0; i < bins; i++) {
      const binStart = minError + i * binSize;
      const binEnd = binStart + binSize;
      binLabels.push(`${binStart.toFixed(1)}-${binEnd.toFixed(1)}%`);
      
      errors.forEach(error => {
        if (error >= binStart && error < binEnd) {
          histogram[i]++;
        }
      });
    }

    return {
      backgroundColor: 'transparent',
      title: {
        text: 'Prediction Error Distribution',
        textStyle: { color: '#fff' },
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#2a2a2a',
        borderColor: '#666',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: binLabels,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { 
          color: '#999',
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' },
        splitLine: { lineStyle: { color: '#333' } }
      },
      series: [{
        type: 'bar',
        data: histogram,
        itemStyle: {
          color: '#f7931a',
          borderRadius: [4, 4, 0, 0]
        }
      }]
    };
  };

  const getPerformanceTrendChartOptions = () => {
    if (!backtestData || backtestData.length === 0) return {};

    const trendData = PerformanceAnalyzer.generatePerformanceTrend(backtestData);

    return {
      backgroundColor: 'transparent',
      title: {
        text: 'Model Performance Trend',
        textStyle: { color: '#fff' },
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#2a2a2a',
        borderColor: '#666',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['Accuracy %', 'Error %'],
        textStyle: { color: '#999' },
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: trendData.dates,
        axisLine: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#999' }
      },
      yAxis: [
        {
          type: 'value',
          name: 'Accuracy %',
          position: 'left',
          axisLine: { lineStyle: { color: '#00da3c' } },
          axisLabel: { color: '#00da3c' },
          splitLine: { lineStyle: { color: '#333' } }
        },
        {
          type: 'value',
          name: 'Error %',
          position: 'right',
          axisLine: { lineStyle: { color: '#ec0000' } },
          axisLabel: { color: '#ec0000' }
        }
      ],
      series: [
        {
          name: 'Accuracy %',
          type: 'line',
          yAxisIndex: 0,
          data: trendData.accuracy,
          smooth: true,
          lineStyle: { color: '#00da3c', width: 2 },
          itemStyle: { color: '#00da3c' }
        },
        {
          name: 'Error %',
          type: 'line',
          yAxisIndex: 1,
          data: trendData.errors,
          smooth: true,
          lineStyle: { color: '#ec0000', width: 2 },
          itemStyle: { color: '#ec0000' }
        }
      ]
    };
  };

  const errorTemplate = (rowData: BacktestResult) => {
    const errorPercent = rowData.percentageError;
    const color = getMetricColor(errorPercent, 'error');
    return (
      <span style={{ color }}>
        {errorPercent.toFixed(2)}%
      </span>
    );
  };

  const priceTemplate = (value: number) => {
    return `$${value.toLocaleString()}`;
  };

  if (loading) {
    return (
      <div className="p-4" style={{ backgroundColor: '#1a1a1a', minHeight: '100vh' }}>
        <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
          <Skeleton height="400px" />
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4" style={{ backgroundColor: '#1a1a1a', minHeight: '100vh' }}>
        <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
          <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#ff6b6b' }}>
            Error: {error}
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4" style={{ backgroundColor: '#1a1a1a', minHeight: '100vh', color: '#ffffff' }}>
      {/* Performance Metrics Cards */}
      <div className="grid mb-4">
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Model Accuracy</div>
            <div 
              className="text-3xl font-bold mb-2"
              style={{ color: getMetricColor(performanceMetrics?.accuracy || 0, 'accuracy') }}
            >
              {(performanceMetrics?.accuracy || 0).toFixed(1)}%
            </div>
            <ProgressBar 
              value={performanceMetrics?.accuracy || 0} 
              style={{ height: '8px' }}
              color={getMetricColor(performanceMetrics?.accuracy || 0, 'accuracy')}
            />
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Directional Accuracy</div>
            <div 
              className="text-3xl font-bold mb-2"
              style={{ color: getMetricColor(performanceMetrics?.directionalAccuracy || 0, 'accuracy') }}
            >
              {(performanceMetrics?.directionalAccuracy || 0).toFixed(1)}%
            </div>
            <ProgressBar 
              value={performanceMetrics?.directionalAccuracy || 0} 
              style={{ height: '8px' }}
              color={getMetricColor(performanceMetrics?.directionalAccuracy || 0, 'accuracy')}
            />
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Mean Error (MAPE)</div>
            <div 
              className="text-3xl font-bold mb-2"
              style={{ color: getMetricColor(performanceMetrics?.mape || 0, 'error') }}
            >
              {(performanceMetrics?.mape || 0).toFixed(1)}%
            </div>
            <ProgressBar 
              value={Math.min(100, performanceMetrics?.mape || 0)} 
              style={{ height: '8px' }}
              color={getMetricColor(performanceMetrics?.mape || 0, 'error')}
            />
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040', textAlign: 'center' }}>
            <div className="text-sm text-500 mb-2">Data Quality</div>
            <div 
              className="text-3xl font-bold mb-2"
              style={{ color: getMetricColor(performanceMetrics?.dataQuality || 0, 'accuracy') }}
            >
              {(performanceMetrics?.dataQuality || 0).toFixed(0)}%
            </div>
            <ProgressBar 
              value={performanceMetrics?.dataQuality || 0} 
              style={{ height: '8px' }}
              color={getMetricColor(performanceMetrics?.dataQuality || 0, 'accuracy')}
            />
          </Card>
        </div>
      </div>

      {/* Model Reliability */}
      <Card className="mb-4" style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <h3 className="text-white mb-3">Model Reliability Indicators</h3>
        <div className="grid">
          <div className="col-12 md:col-2-4">
            <div className="text-center">
              <div className="text-sm text-500 mb-2">Overall</div>
              <Badge 
                value={getReliabilityBadge(modelReliability?.overall || 0).label}
                severity={getReliabilityBadge(modelReliability?.overall || 0).severity}
                className="mb-2"
              />
              <div className="text-lg font-semibold">{(modelReliability?.overall || 0).toFixed(1)}%</div>
            </div>
          </div>
          <div className="col-12 md:col-2-4">
            <div className="text-center">
              <div className="text-sm text-500 mb-2">Short Term</div>
              <Badge 
                value={getReliabilityBadge(modelReliability?.shortTerm || 0).label}
                severity={getReliabilityBadge(modelReliability?.shortTerm || 0).severity}
                className="mb-2"
              />
              <div className="text-lg font-semibold">{(modelReliability?.shortTerm || 0).toFixed(1)}%</div>
            </div>
          </div>
          <div className="col-12 md:col-2-4">
            <div className="text-center">
              <div className="text-sm text-500 mb-2">Long Term</div>
              <Badge 
                value={getReliabilityBadge(modelReliability?.longTerm || 0).label}
                severity={getReliabilityBadge(modelReliability?.longTerm || 0).severity}
                className="mb-2"
              />
              <div className="text-lg font-semibold">{(modelReliability?.longTerm || 0).toFixed(1)}%</div>
            </div>
          </div>
          <div className="col-12 md:col-2-4">
            <div className="text-center">
              <div className="text-sm text-500 mb-2">Volatility Adj.</div>
              <Badge 
                value={getReliabilityBadge(modelReliability?.volatilityAdjusted || 0).label}
                severity={getReliabilityBadge(modelReliability?.volatilityAdjusted || 0).severity}
                className="mb-2"
              />
              <div className="text-lg font-semibold">{(modelReliability?.volatilityAdjusted || 0).toFixed(1)}%</div>
            </div>
          </div>
          <div className="col-12 md:col-2-4">
            <div className="text-center">
              <div className="text-sm text-500 mb-2">Trend Accuracy</div>
              <Badge 
                value={getReliabilityBadge(modelReliability?.trendAccuracy || 0).label}
                severity={getReliabilityBadge(modelReliability?.trendAccuracy || 0).severity}
                className="mb-2"
              />
              <div className="text-lg font-semibold">{(modelReliability?.trendAccuracy || 0).toFixed(1)}%</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Charts and Detailed Analysis */}
      <Card style={{ backgroundColor: '#2a2a2a', border: '1px solid #404040' }}>
        <TabView>
          <TabPanel header="Actual vs Predicted" leftIcon="pi pi-chart-line">
            <ReactECharts
              option={getActualVsPredictedChartOptions()}
              style={{ height: '400px' }}
              opts={{ renderer: 'canvas' }}
            />
          </TabPanel>
          
          <TabPanel header="Error Distribution" leftIcon="pi pi-chart-bar">
            <ReactECharts
              option={getErrorDistributionChartOptions()}
              style={{ height: '400px' }}
              opts={{ renderer: 'canvas' }}
            />
          </TabPanel>
          
          <TabPanel header="Performance Trend" leftIcon="pi pi-trending-up">
            <ReactECharts
              option={getPerformanceTrendChartOptions()}
              style={{ height: '400px' }}
              opts={{ renderer: 'canvas' }}
            />
          </TabPanel>
          
          <TabPanel header="Backtest Results" leftIcon="pi pi-table">
            <DataTable 
              value={backtestData.slice(-50)} // Show last 50 results
              className="p-datatable-sm"
              paginator
              rows={10}
              emptyMessage="No backtest data available"
            >
              <Column field="date" header="Date" />
              <Column field="actualPrice" header="Actual Price" body={(data) => priceTemplate(data.actualPrice)} />
              <Column field="predictedPrice" header="Predicted Price" body={(data) => priceTemplate(data.predictedPrice)} />
              <Column field="error" header="Error ($)" body={(data) => `$${Math.abs(data.error).toLocaleString()}`} />
              <Column field="percentageError" header="Error %" body={errorTemplate} />
            </DataTable>
          </TabPanel>
        </TabView>
      </Card>

      {/* Last Updated Info */}
      <div className="mt-3 text-center text-500 text-sm">
        Last updated: {performanceMetrics?.lastUpdated.toLocaleString()}
      </div>
    </div>
  );
};

export default ModelPerformance;
